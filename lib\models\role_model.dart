class RoleModel {
  final String id;
  final String title;
  final String description;
  final String iconPath;

  RoleModel({
    required this.id,
    required this.title,
    required this.description,
    required this.iconPath,
  });

  static List<RoleModel> roles = [
    RoleModel(
      id: 'patient',
      title: 'Patient',
      description: 'Track your health metrics and manage your condition.',
      iconPath: 'assets/images/animation.patient_phone.gif',
    ),
    RoleModel(
      id: 'doctor',
      title: 'Doctor',
      description: 'Monitor patients and provide medical guidance.',
      iconPath: 'assets/images/animation_doctor.gif',
    ),
  ];
}
