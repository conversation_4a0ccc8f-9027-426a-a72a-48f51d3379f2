import 'package:flutter/material.dart';
import 'package:befine/pages/welcom/onboarding_screen.dart';
import 'package:befine/pages/welcom/welcome_screen.dart';
import 'package:befine/pages/sign/sign_in_screen.dart';
import 'package:befine/pages/sign/signup_doctor_screen.dart';
import 'package:befine/pages/sign/signup_patient_screen.dart';
import 'package:befine/pages/patient/patient_main_page.dart';
import 'package:befine/pages/patient/manage_devices_screen.dart';
import 'package:befine/pages/patient/device_info_page.dart';
import 'package:befine/pages/doctor/doctor_main_page.dart';
import 'package:befine/pages/chat/chat_screen.dart';

class AppRoutes {
  // Route names
  static const String onboarding = '/';
  static const String welcome = '/welcome';
  static const String signIn = '/sign-in';
  static const String signUpDoctor = '/sign-up-doctor';
  static const String signUpPatient = '/sign-up-patient';
  static const String patientMain = '/PatientMain';
  static const String doctorMain = '/DoctorMainPage';
  static const String manageDevices = '/manage-devices';
  static const String deviceInfo = '/device-info';
  static const String chat = '/chat';

  // Route generator
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case onboarding:
        return MaterialPageRoute(builder: (_) => const OnboardingScreen());
      case welcome:
        return MaterialPageRoute(builder: (_) => const WelcomeScreen());
      case signIn:
        return MaterialPageRoute(builder: (_) => const SignInScreen());
      case signUpDoctor:
        return MaterialPageRoute(builder: (_) => const SignUpDoctorScreen());
      case signUpPatient:
        return MaterialPageRoute(builder: (_) => const SignUpPatientScreen());
      case patientMain:
        return MaterialPageRoute(
          builder: (_) => const PatientMainPage(),
          settings: const RouteSettings(name: patientMain),
        );
      case doctorMain:
        return MaterialPageRoute(
          builder: (_) => const DoctorMainPage(),
          settings: const RouteSettings(name: doctorMain),
        );
      case manageDevices:
        return MaterialPageRoute(builder: (_) => const ManageDevicesScreen());
      case deviceInfo:
        return MaterialPageRoute(builder: (_) => const DeviceInfoPage());
      case chat:
        return MaterialPageRoute(builder: (_) => const ChatScreen());
      default:
        return MaterialPageRoute(
          builder:
              (_) => Scaffold(
                body: Center(
                  child: Text('No route defined for ${settings.name}'),
                ),
              ),
        );
    }
  }

  // Navigation helpers
  static void navigateToPatientMain(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      patientMain,
      (route) => false, // Remove all previous routes
    );
  }

  static void navigateToDoctorMain(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      doctorMain,
      (route) => false, // Remove all previous routes
    );
  }

  static void navigateToWelcome(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      welcome,
      (route) => false, // Remove all previous routes
    );
  }
}
