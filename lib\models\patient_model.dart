/// Patient model class for storing and managing patient information
/// Designed for future Supabase integration
class PatientModel {
  /// Required fields
  final String firstName;
  final String lastName;
  final String gender;
  final String dateOfBirth;
  final double height;
  final double weight;
  final String phoneNumber;
  final String email;
  final String password;

  /// Optional fields with default values
  final String smokingStatus;
  final String bloodType;
  final String activityLevel;
  final String emergencyContact;
  final String notes;

  /// Location/Address fields
  final String address;

  /// Profile image path
  final String? profileImagePath;

  PatientModel({
    // Required fields
    required this.firstName,
    required this.lastName,
    required this.gender,
    required this.dateOfBirth,
    required this.height,
    required this.weight,
    required this.phoneNumber,
    required this.email,
    required this.password,

    // Optional fields with default values
    this.smokingStatus = 'Unknown',
    this.bloodType = 'Unknown',
    this.activityLevel = 'Unknown',
    this.emergencyContact = 'Unknown',
    this.notes = 'Unknown',

    // Location/Address fields with default values
    this.address = 'Unknown',

    // Profile image path
    this.profileImagePath,
  });

  /// Demo patient data for testing and development
  static List<PatientModel> demoPatients = [
    PatientModel(
      firstName: '<PERSON>',
      lastName: 'Doe',
      dateOfBirth: '1985-03-15',
      height: 175,
      weight: 80,
      email: '<EMAIL>',
      password: 'Password123!',
      gender: 'male',
      phoneNumber: '+**********',
      bloodType: 'A+',
      activityLevel: 'Moderate',
      address: '123 Main St, New York, NY 10001',
    ),
    PatientModel(
      firstName: 'Jane',
      lastName: 'Smith',
      gender: 'female',
      dateOfBirth: '1992-08-24',
      height: 165,
      weight: 62,
      phoneNumber: '+**********',
      email: '<EMAIL>',
      password: 'SecurePass456!',
      smokingStatus: 'Former smoker',
      emergencyContact: '+**********',
      notes: 'Peanut allergy',
      address: '456 Oak Ave, Los Angeles, CA 90210',
    ),
    PatientModel(
      firstName: 'Alice',
      lastName: 'Johnson',
      gender: 'female',
      dateOfBirth: '1978-12-01',
      height: 168,
      weight: 58,
      phoneNumber: '+**********',
      email: '<EMAIL>',
      password: 'AlicePass789!',
      bloodType: 'O-',
      activityLevel: 'Active',
      smokingStatus: 'Never smoked',
      address: '789 Pine St, Chicago, IL 60601',
    ),
  ];
}
