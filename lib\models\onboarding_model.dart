class OnboardingModel {
  final String title;
  final String description;
  final String imagePath;

  OnboardingModel({
    required this.title,
    required this.description,
    required this.imagePath,
  });

  static List<OnboardingModel> onboardingContents = [
    OnboardingModel(
      title: 'Welcome to BeFine',
      description: 'Your personal SmartHealer for managing Asthma and COPD .',
      imagePath: 'assets/images/befine_logo.png',
    ),
    OnboardingModel(
      title: 'Monitor Your Health',
      description:
          'Track your respiratory health metrics in real-time with our IoT device.',
      imagePath: 'assets/images/multiple_devices.png',
    ),
    OnboardingModel(
      title: 'Quick-Launch Asthma & COPD Monitoring Software',
      description:
          'Fast & Easy to Use toollkit for patients & doctors.',
      imagePath: 'assets/images/software_toolkit.png',
    ),
  ];
}
