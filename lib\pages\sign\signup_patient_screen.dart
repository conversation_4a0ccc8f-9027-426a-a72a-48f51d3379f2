import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:intl/intl.dart';

class SignUpPatientScreen extends StatefulWidget {
  const SignUpPatientScreen({super.key});

  @override
  State<SignUpPatientScreen> createState() => _SignUpPatientScreenState();
}

class _SignUpPatientScreenState extends State<SignUpPatientScreen> {
  bool _isMale = true;
  final _formKey = GlobalKey<FormState>();

  // Essential Information
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _telController = TextEditingController();
  final _birthdayController = TextEditingController();
  String? _selectedGender;

  // Medical Information (Optional)
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  final _sosTelController = TextEditingController();
  final _notesController = TextEditingController();
  String? _selectedSmokingStatus;
  String? _selectedActivityLevel;
  String? _selectedBloodType;

  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _showOptionalFields = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _telController.dispose();
    _birthdayController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _sosTelController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _handleSignUp() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      try {
        // Validate height and weight as decimal(5,2)
        final height = double.tryParse(_heightController.text);
        final weight = double.tryParse(_weightController.text);
        if (height == null || height <= 0 || height >= 1000) {
          throw Exception('Invalid height value');
        }
        if (weight == null || weight <= 0 || weight >= 1000) {
          throw Exception('Invalid weight value');
        }

        // Validate phone numbers
        if (_telController.text.isNotEmpty &&
            !_isValidPhoneNumber(_telController.text)) {
          throw Exception('Invalid phone number format');
        }
        if (_sosTelController.text.isNotEmpty &&
            !_isValidPhoneNumber(_sosTelController.text)) {
          throw Exception('Invalid SOS phone number format');
        }

        // Validate birthday
        if (!_isValidBirthday(_birthdayController.text)) {
          throw Exception('Invalid birthday format (YYYY-MM-DD)');
        }

        // Set default values for dropdowns if they're null
        final bloodType = _selectedBloodType ?? 'Unknown';
        final smokingStatus = _selectedSmokingStatus ?? 'Unknown';
        final activityLevel = _selectedActivityLevel ?? 'Unknown';

        final userData = {
          'email': _emailController.text,
          'password_hash':
              _passwordController.text, // Note: Hash this before sending
          'role': 'patient',
          'gender': _isMale ? 'male' : 'female',
        };

        final patientData = {
          'first_name': _firstNameController.text,
          'last_name': _lastNameController.text,
          'phone_number': _telController.text,
          'gender': _selectedGender,
          'date_of_birth': _birthdayController.text,
          'height': height,
          'weight': weight,
          'emergency_contact': _sosTelController.text,
          'notes': _notesController.text,
          'smoking_status': smokingStatus,
          'activity_level': activityLevel,
          'blood_type': bloodType,
        };

        // TODO: Implement API call here
        await Future.delayed(const Duration(seconds: 2));

        if (mounted) {
          Navigator.pushReplacementNamed(context, '/PatientMain');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(e.toString()), backgroundColor: Colors.red),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  bool _isValidPhoneNumber(String phone) {
    // Add your phone number validation logic here
    // Example: Tunisian phone number format
    final phoneRegex = RegExp(r'^\+?216?[0-9]{8}$');
    return phoneRegex.hasMatch(phone);
  }

  bool _isValidBirthday(String date) {
    try {
      final parsedDate = DateTime.parse(date);
      final now = DateTime.now();
      return parsedDate.isBefore(now) && parsedDate.year > 1900;
    } catch (e) {
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize dropdown values as null to show hints
    _selectedGender = 'male';
    _selectedSmokingStatus = null;
    _selectedActivityLevel = null;
    _selectedBloodType = null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
          physics: const BouncingScrollPhysics(),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(),
                _buildEssentialInformation(),
                _buildOptionalInformationToggle(),
                if (_showOptionalFields) _buildMedicalInformation(),
                _buildSignUpButton(),
                _buildSignInLink(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        const SizedBox(height: 5),
        Center(
          child: Image.asset(
            'assets/images/animation_checkup.gif',
            height: 150,
          ),
        ),
        const SizedBox(height: 10),
        Text(
          'Create Patient Account',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Please fill in the essential information to get started',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppTheme.textSecondaryColor),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildEssentialInformation() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Essential Information', Icons.person_outline),
        const SizedBox(height: 16),
        _buildGenderToggle(),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                controller: _firstNameController,
                label: 'First Name',
                icon: Icons.person_outline,
                required: true,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTextField(
                controller: _lastNameController,
                label: 'Last Name',
                icon: Icons.person_outline,
                required: true,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),
        _buildDatePicker(),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                controller: _heightController,
                label: 'Height (cm)',
                icon: Icons.height,
                keyboardType: TextInputType.number,
                required: true, // Changed from false to true
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Height is required';
                  }
                  final height = double.tryParse(value);
                  if (height == null || height <= 0 || height >= 300) {
                    return 'Enter a valid height';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTextField(
                controller: _weightController,
                label: 'Weight (kg)',
                icon: Icons.monitor_weight,
                keyboardType: TextInputType.number,
                required: true, // Changed from false to true
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Weight is required';
                  }
                  final weight = double.tryParse(value);
                  if (weight == null || weight <= 0 || weight >= 500) {
                    return 'Enter a valid weight';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildSectionHeader('Account Information', Icons.lock_outline),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _emailController,
          label: 'Email Address',
          icon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          required: true,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Email is required';
            }
            if (!value.contains('@')) {
              return 'Enter a valid email address';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildPasswordField(),
        const SizedBox(height: 16),
        _buildConfirmPasswordField(),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildOptionalInformationToggle() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            setState(() {
              _showOptionalFields = !_showOptionalFields;
            });
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _showOptionalFields
                      ? Icons.keyboard_arrow_up_rounded
                      : Icons.keyboard_arrow_down_rounded,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  _showOptionalFields
                      ? 'Hide Medical Information'
                      : 'Add Medical Information (Optional)',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMedicalInformation() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          'Medical Information',
          Icons.medical_services_outlined,
        ),
        const SizedBox(height: 16),

        _buildTextField(
          controller: _telController,
          label: 'Phone Number',
          icon: Icons.phone,
          keyboardType: TextInputType.phone,
          required: false, // Changed from true to false
          validator: (value) {
            if (value?.isEmpty ?? true) return null; // Allow empty value
            return !_isValidPhoneNumber(value!)
                ? 'Enter a valid phone number'
                : null;
          },
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          value: _selectedBloodType,
          label: 'Blood Type',
          icon: Icons.bloodtype,
          items: const [
            'Unknown',
            'A+',
            'A-',
            'B+',
            'B-',
            'AB+',
            'AB-',
            'O+',
            'O-',
          ],
          onChanged: (value) => setState(() => _selectedBloodType = value),
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          value: _selectedSmokingStatus,
          label: 'Smoking Status',
          icon: Icons.smoking_rooms,
          items: const [
            'Unknown',
            'Never smoked',
            'Former smoker',
            'Current Smoker',
            'Passive Smoker',
          ],
          onChanged: (value) => setState(() => _selectedSmokingStatus = value),
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          value: _selectedActivityLevel,
          label: 'Activity Level',
          icon: Icons.directions_run,
          items: const [
            'Unknown',
            'Inactive',
            'Lightly Active',
            'Moderately Active',
            'Very Active',
          ],
          onChanged: (value) => setState(() => _selectedActivityLevel = value),
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _sosTelController,
          label: 'Emergency Contact',
          icon: Icons.emergency,
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value?.isEmpty ?? true) return null;
            return !_isValidPhoneNumber(value!)
                ? 'Enter a valid phone number'
                : null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _notesController,
          label: 'Medical Notes',
          icon: Icons.note,
          maxLines: 3,
          hintText: 'Enter any allergies, conditions, or important notes',
        ),
      ],
    );
  }

  Widget _buildSignUpButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSignUp,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child:
            _isLoading
                ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                : const Text('Create Account'),
      ),
    );
  }

  Widget _buildSignInLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Already have an account? ',
          style: TextStyle(color: AppTheme.textSecondaryColor),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(foregroundColor: AppTheme.primaryColor),
          child: const Text('Sign In'),
        ),
      ],
    );
  }

  // Helper Widgets
  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor, size: 24),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    bool required = false,
    int maxLines = 1,
    String? hintText,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: required ? '$label*' : label,
        hintText: hintText,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
      ),
      validator:
          required
              ? (value) =>
                  value?.isEmpty ?? true
                      ? '$label is required'
                      : validator?.call(value)
              : validator,
    );
  }

  Widget _buildDropdownField({
    required String? value,
    required String label,
    required IconData icon,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    // Show hint if value is "Unknown"
    final displayValue = value == "Unknown" ? null : value;

    return DropdownButtonFormField<String>(
      value: displayValue,
      dropdownColor: Colors.white,
      icon: const Icon(Icons.arrow_drop_down),
      elevation: 8,
      isExpanded: true,
      style: const TextStyle(fontSize: 16, color: Colors.black87),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
          borderSide: BorderSide(color: AppTheme.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
        ),
      ),
      menuMaxHeight: 300,
      borderRadius: BorderRadius.circular(AppTheme.defaultBorderRadius),
      items:
          items
              .map(
                (item) => DropdownMenuItem<String>(
                  value: item,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(item),
                  ),
                ),
              )
              .toList(),
      onChanged: onChanged,
      hint: Text(
        'Select $label',
        style: TextStyle(color: Colors.grey.shade600),
      ),
    );
  }

  Widget _buildDatePicker() {
    return TextFormField(
      controller: _birthdayController,
      decoration: InputDecoration(
        labelText: 'Date of Birth*',
        prefixIcon: const Icon(Icons.calendar_today),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      readOnly: true,
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: DateTime.now().subtract(
            const Duration(days: 6570),
          ), // 18 years ago
          firstDate: DateTime(1900),
          lastDate: DateTime.now(),
        );
        if (date != null) {
          setState(() {
            _birthdayController.text = DateFormat('yyyy-MM-dd').format(date);
          });
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Date of birth is required';
        }
        if (!_isValidBirthday(value)) {
          return 'Please enter a valid date';
        }
        return null;
      },
    );
  }

  Widget _buildGenderToggle() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => setState(() => _isMale = true),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(11),
                  color: _isMale ? AppTheme.primaryColor : Colors.transparent,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.male_rounded,
                      color: _isMale ? Colors.white : Colors.grey,
                      size: 28,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Male',
                      style: TextStyle(
                        color: _isMale ? Colors.white : Colors.grey,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: () => setState(() => _isMale = false),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(11),
                  color: !_isMale ? AppTheme.primaryColor : Colors.transparent,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.female_rounded,
                      color: !_isMale ? Colors.white : Colors.grey,
                      size: 28,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Female',
                      style: TextStyle(
                        color: !_isMale ? Colors.white : Colors.grey,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      decoration: InputDecoration(
        labelText: 'Password*',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Password is required';
        }
        if (value.length < 6) {
          return 'Password must be at least 6 characters';
        }
        return null;
      },
    );
  }

  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      obscureText: !_isPasswordVisible,
      decoration: InputDecoration(
        labelText: 'Confirm Password*',
        prefixIcon: const Icon(Icons.lock_outline),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please confirm your password';
        }
        if (value != _passwordController.text) {
          return 'Passwords do not match';
        }
        return null;
      },
    );
  }
}
