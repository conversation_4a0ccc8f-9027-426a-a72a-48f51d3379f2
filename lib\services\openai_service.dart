import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:befine/models/chat_message.dart';

/// Service for handling OpenRouter API communication with GPT models
class OpenAIService extends ChangeNotifier {
  static const String _apiKey =
      '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';
  static const String _baseUrl = 'https://openrouter.ai/api/v1';
  static const String _model = 'openai/gpt-4o-mini';

  // System prompt for the medical assistant
  static const String _systemPrompt =
      '''You are a professional, compassionate AI health assistant specializing in supporting patients with chronic respiratory conditions such as COPD and Asthma. Your role is to educate, support, and guide patients — not to diagnose or prescribe treatment.

Always be empathetic and respectful in tone. Use simple, clear, and non-alarming language. If symptoms sound severe or life-threatening, recommend immediate medical attention.

Key areas you can help with:
- Inhaler technique and usage
- Medication adherence and management
- Breathing exercises and techniques
- Lifestyle modifications for respiratory health
- Understanding symptoms and when to seek help
- General respiratory health education

Always remind patients to consult their healthcare provider for medical decisions and never replace professional medical advice.''';

  final List<ChatMessage> _messages = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<ChatMessage> get messages => List.unmodifiable(_messages);
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize the OpenAI service
  OpenAIService() {
    _initializeOpenAI();
    _addWelcomeMessage();
  }

  /// Initialize OpenRouter service
  void _initializeOpenAI() {
    debugPrint(
      'Initializing OpenRouter with API key: ${_apiKey.substring(0, 20)}...',
    );
    debugPrint('OpenRouter initialized successfully');
  }

  /// Add welcome message from assistant
  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage.assistant(
      content:
          "Hello! I'm your specialized BeFine medical assistant. I'm here to help you with questions about your respiratory health, inhaler usage, medication management, and breathing techniques. How can I assist you today?",
    );
    _messages.add(welcomeMessage);
    notifyListeners();
  }

  /// Send a message and get response from OpenRouter GPT
  Future<void> sendMessage(String userMessage) async {
    if (userMessage.trim().isEmpty) return;

    // Clear any previous errors
    _clearError();

    // Add user message
    final userChatMessage = ChatMessage.user(content: userMessage.trim());
    _messages.add(userChatMessage);

    // Add loading message
    final loadingMessage = ChatMessage.loading();
    _messages.add(loadingMessage);

    _setLoading(true);
    notifyListeners();

    try {
      debugPrint('Sending message to OpenRouter GPT: $userMessage');

      // Prepare conversation history for the API
      final conversationMessages = _buildConversationHistory();

      // Call OpenRouter chat completion API
      final response = await _callChatCompletion(conversationMessages);

      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      // Add assistant response
      final assistantMessage = ChatMessage.assistant(content: response);
      _messages.add(assistantMessage);
    } catch (e) {
      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      // Add error message
      _setError('Failed to get response: ${e.toString()}');

      // Enhanced debugging
      debugPrint('=== DETAILED ERROR INFORMATION ===');
      debugPrint('Error: $e');
      debugPrint('Error type: ${e.runtimeType}');
      debugPrint('Error details: ${e.toString()}');
      debugPrint('=== END ERROR INFORMATION ===');

      // Provide more specific error messages based on the actual error
      String errorMessage;
      final errorString = e.toString().toLowerCase();

      if (errorString.contains('401') || errorString.contains('unauthorized')) {
        errorMessage =
            "I'm having trouble with my configuration. Please contact support.";
      } else if (errorString.contains('404') ||
          errorString.contains('not found')) {
        errorMessage =
            "I'm having trouble accessing my assistant configuration. Please contact support.";
      } else if (errorString.contains('429') ||
          errorString.contains('rate limit')) {
        errorMessage =
            "I'm currently experiencing high demand. Please try again in a few moments.";
      } else if (errorString.contains('500') ||
          errorString.contains('server')) {
        errorMessage =
            "I'm experiencing server issues. Please try again later.";
      } else if (errorString.contains('network') ||
          errorString.contains('connection') ||
          errorString.contains('timeout')) {
        errorMessage =
            "I'm having trouble connecting right now. Please check your internet connection and try again.";
      } else {
        errorMessage =
            "I'm experiencing a temporary issue. Please try asking your question again. (Debug: ${e.toString()})";
      }

      final assistantErrorMessage = ChatMessage.assistant(
        content: errorMessage,
      );
      _messages.add(assistantErrorMessage);
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  /// Build conversation history for OpenRouter API
  List<Map<String, String>> _buildConversationHistory() {
    final conversationMessages = <Map<String, String>>[];

    // Add system prompt
    conversationMessages.add({'role': 'system', 'content': _systemPrompt});

    // Add conversation history (excluding loading messages)
    for (final message in _messages) {
      if (!message.isLoading) {
        conversationMessages.add({
          'role': message.sender == MessageSender.user ? 'user' : 'assistant',
          'content': message.content,
        });
      }
    }

    return conversationMessages;
  }

  /// Call OpenRouter chat completion API
  Future<String> _callChatCompletion(List<Map<String, String>> messages) async {
    debugPrint('Calling OpenRouter chat completion...');

    final response = await http.post(
      Uri.parse('$_baseUrl/chat/completions'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'HTTP-Referer':
            'https://befine.app', // Optional: for OpenRouter analytics
        'X-Title':
            'BeFine Medical Assistant', // Optional: for OpenRouter analytics
      },
      body: json.encode({
        'model': _model,
        'messages': messages,
        'max_tokens': 1000,
        'temperature': 0.7,
        'stream': false,
      }),
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      final content = responseData['choices'][0]['message']['content'];
      debugPrint(
        'OpenRouter response received: ${content.substring(0, 100)}...',
      );
      return content;
    } else {
      debugPrint(
        'OpenRouter API error: ${response.statusCode} - ${response.body}',
      );
      throw Exception(
        'Failed to get response from OpenRouter: ${response.statusCode} - ${response.body}',
      );
    }
  }

  /// Clear conversation history
  void clearConversation() {
    _messages.clear();
    _clearError();
    _addWelcomeMessage();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Test OpenRouter API connection
  Future<void> testAPI() async {
    debugPrint('=== TESTING OPENROUTER API CONNECTION ===');

    try {
      // Test: Check API key by making a simple chat completion request
      debugPrint('Testing OpenRouter API key with a simple request...');

      final testMessages = [
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        {'role': 'user', 'content': 'Hello, this is a test message.'},
      ];

      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
          'HTTP-Referer': 'https://befine.app',
          'X-Title': 'BeFine Medical Assistant Test',
        },
        body: json.encode({
          'model': _model,
          'messages': testMessages,
          'max_tokens': 50,
          'temperature': 0.7,
        }),
      );

      debugPrint('OpenRouter API Status: ${response.statusCode}');
      debugPrint('OpenRouter API Response: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final content = responseData['choices'][0]['message']['content'];
        debugPrint('✅ OpenRouter API is working! Test response: $content');
      } else {
        debugPrint('❌ OpenRouter API key is invalid or has issues');
      }
    } catch (e) {
      debugPrint('❌ Test failed with error: $e');
    }

    debugPrint('=== END OPENROUTER API TEST ===');
  }

  /// Dispose resources
  @override
  void dispose() {
    super.dispose();
  }
}
