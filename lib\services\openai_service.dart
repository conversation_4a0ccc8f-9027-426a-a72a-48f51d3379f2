import 'package:flutter/foundation.dart';
import 'package:dart_openai/dart_openai.dart';
import 'package:befine/models/chat_message.dart';

/// Service for handling OpenAI ChatGPT API communication
class OpenAIService extends ChangeNotifier {
  static const String _apiKey =
      '********************************************************************************************************************************************************************';

  List<ChatMessage> _messages = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<ChatMessage> get messages => List.unmodifiable(_messages);
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize the OpenAI service
  OpenAIService() {
    _initializeOpenAI();
    _addWelcomeMessage();
  }

  /// Initialize OpenAI with API key
  void _initializeOpenAI() {
    OpenAI.apiKey = _apiKey;
    OpenAI.showLogs = kDebugMode;
  }

  /// Add welcome message from assistant
  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage.assistant(
      content:
          "Hello! I'm your medical assistant. I'm here to help you with questions about your respiratory health, inhaler usage, and general medical concerns. How can I assist you today?",
    );
    _messages.add(welcomeMessage);
    notifyListeners();
  }

  /// Send a message and get response from ChatGPT
  Future<void> sendMessage(String userMessage) async {
    if (userMessage.trim().isEmpty) return;

    // Clear any previous errors
    _clearError();

    // Add user message
    final userChatMessage = ChatMessage.user(content: userMessage.trim());
    _messages.add(userChatMessage);

    // Add loading message
    final loadingMessage = ChatMessage.loading();
    _messages.add(loadingMessage);

    _setLoading(true);
    notifyListeners();

    try {
      // Prepare conversation history for context
      final conversationHistory = _buildConversationHistory();

      // Create chat completion request
      final chatCompletion = await OpenAI.instance.chat.create(
        model: "gpt-3.5-turbo",
        messages: conversationHistory,
        maxTokens: 500,
        temperature: 0.7,
        topP: 1.0,
        frequencyPenalty: 0.0,
        presencePenalty: 0.0,
      );

      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      // Add assistant response
      if (chatCompletion.choices.isNotEmpty) {
        final assistantResponse =
            chatCompletion.choices.first.message.content?.first.text ??
            "I apologize, but I couldn't generate a response. Please try again.";

        final assistantMessage = ChatMessage.assistant(
          content: assistantResponse,
        );
        _messages.add(assistantMessage);
      }
    } catch (e) {
      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      // Add error message
      _setError('Failed to get response: ${e.toString()}');

      final errorMessage = ChatMessage.assistant(
        content:
            "I apologize, but I'm having trouble connecting right now. Please check your internet connection and try again.",
      );
      _messages.add(errorMessage);

      debugPrint('OpenAI API Error: $e');
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  /// Build conversation history for context
  List<OpenAIChatCompletionChoiceMessageModel> _buildConversationHistory() {
    final systemMessage = OpenAIChatCompletionChoiceMessageModel(
      role: OpenAIChatMessageRole.system,
      content: [
        OpenAIChatCompletionChoiceMessageContentItemModel.text(
          "You are a helpful medical assistant specializing in respiratory health and inhaler usage. "
          "Provide accurate, helpful information about respiratory conditions, inhaler techniques, "
          "medication adherence, and general health advice. Always remind users to consult with "
          "their healthcare provider for serious concerns. Keep responses concise and professional. "
          "Focus on practical advice for managing respiratory conditions and proper inhaler usage.",
        ),
      ],
    );

    final conversationMessages =
        _messages
            .where((msg) => !msg.isLoading && msg.content.isNotEmpty)
            .map(
              (msg) => OpenAIChatCompletionChoiceMessageModel(
                role:
                    msg.sender == MessageSender.user
                        ? OpenAIChatMessageRole.user
                        : OpenAIChatMessageRole.assistant,
                content: [
                  OpenAIChatCompletionChoiceMessageContentItemModel.text(
                    msg.content,
                  ),
                ],
              ),
            )
            .toList();

    return [systemMessage, ...conversationMessages];
  }

  /// Clear conversation history
  void clearConversation() {
    _messages.clear();
    _clearError();
    _addWelcomeMessage();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Dispose resources
  @override
  void dispose() {
    super.dispose();
  }
}
