import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:befine/models/chat_message.dart';

/// Service for handling OpenAI Assistant API communication
class OpenAIService extends ChangeNotifier {
  static const String _apiKey =
      '********************************************************************************************************************************************************************';

  // Your specific patient assistant ID
  static const String _assistantId = 'asst_652dWmYXyXoLP87kf74OIgOy';

  List<ChatMessage> _messages = [];
  bool _isLoading = false;
  String? _error;
  String? _threadId; // Thread ID for the assistant conversation

  // Getters
  List<ChatMessage> get messages => List.unmodifiable(_messages);
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize the OpenAI service
  OpenAIService() {
    _initializeOpenAI();
    _addWelcomeMessage();
  }

  /// Initialize OpenAI service
  void _initializeOpenAI() {
    debugPrint(
      'Initializing OpenAI with API key: ${_apiKey.substring(0, 20)}...',
    );
    debugPrint('OpenAI initialized successfully');
  }

  /// Add welcome message from assistant
  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage.assistant(
      content:
          "Hello! I'm your specialized BeFine medical assistant. I'm here to help you with questions about your respiratory health, inhaler usage, medication management, and breathing techniques. How can I assist you today?",
    );
    _messages.add(welcomeMessage);
    notifyListeners();
  }

  /// Send a message and get response from OpenAI Assistant
  Future<void> sendMessage(String userMessage) async {
    if (userMessage.trim().isEmpty) return;

    // Clear any previous errors
    _clearError();

    // Add user message
    final userChatMessage = ChatMessage.user(content: userMessage.trim());
    _messages.add(userChatMessage);

    // Add loading message
    final loadingMessage = ChatMessage.loading();
    _messages.add(loadingMessage);

    _setLoading(true);
    notifyListeners();

    try {
      debugPrint('Sending message to OpenAI Assistant: $userMessage');

      // Step 1: Create or use existing thread
      if (_threadId == null) {
        await _createThread();
      }

      // Step 2: Add message to thread
      await _addMessageToThread(userMessage.trim());

      // Step 3: Run the assistant
      final runId = await _runAssistant();

      // Step 4: Wait for completion and get response
      await _waitForCompletion(runId);
    } catch (e) {
      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      // Add error message
      _setError('Failed to get response: ${e.toString()}');

      // Provide more specific error messages
      String errorMessage;
      if (e.toString().contains('API key')) {
        errorMessage =
            "I'm having trouble with my configuration. Please contact support.";
      } else if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        errorMessage =
            "I'm having trouble connecting right now. Please check your internet connection and try again.";
      } else if (e.toString().contains('quota') ||
          e.toString().contains('limit')) {
        errorMessage =
            "I'm currently experiencing high demand. Please try again in a few moments.";
      } else {
        errorMessage =
            "I'm experiencing a temporary issue. Please try asking your question again.";
      }

      final assistantErrorMessage = ChatMessage.assistant(
        content: errorMessage,
      );
      _messages.add(assistantErrorMessage);

      debugPrint('OpenAI API Error: $e');
      debugPrint('Error type: ${e.runtimeType}');
      debugPrint('Error details: ${e.toString()}');
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  /// Create a new thread for the assistant conversation
  Future<void> _createThread() async {
    debugPrint('Creating new thread...');

    final response = await http.post(
      Uri.parse('https://api.openai.com/v1/threads'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'OpenAI-Beta': 'assistants=v2',
      },
      body: json.encode({}),
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      _threadId = responseData['id'];
      debugPrint('Thread created: $_threadId');
    } else {
      throw Exception(
        'Failed to create thread: ${response.statusCode} - ${response.body}',
      );
    }
  }

  /// Add a message to the thread
  Future<void> _addMessageToThread(String message) async {
    debugPrint('Adding message to thread: $message');

    final response = await http.post(
      Uri.parse('https://api.openai.com/v1/threads/$_threadId/messages'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'OpenAI-Beta': 'assistants=v2',
      },
      body: json.encode({'role': 'user', 'content': message}),
    );

    if (response.statusCode != 200) {
      throw Exception(
        'Failed to add message: ${response.statusCode} - ${response.body}',
      );
    }

    debugPrint('Message added to thread');
  }

  /// Run the assistant
  Future<String> _runAssistant() async {
    debugPrint('Running assistant...');

    final response = await http.post(
      Uri.parse('https://api.openai.com/v1/threads/$_threadId/runs'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'OpenAI-Beta': 'assistants=v2',
      },
      body: json.encode({'assistant_id': _assistantId}),
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      final runId = responseData['id'];
      debugPrint('Assistant run started: $runId');
      return runId;
    } else {
      throw Exception(
        'Failed to run assistant: ${response.statusCode} - ${response.body}',
      );
    }
  }

  /// Wait for the assistant run to complete and get the response
  Future<void> _waitForCompletion(String runId) async {
    debugPrint('Waiting for completion...');

    while (true) {
      await Future.delayed(const Duration(seconds: 1));

      final response = await http.get(
        Uri.parse('https://api.openai.com/v1/threads/$_threadId/runs/$runId'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'OpenAI-Beta': 'assistants=v2',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final status = responseData['status'];

        debugPrint('Run status: $status');

        if (status == 'completed') {
          await _getLatestMessage();
          break;
        } else if (status == 'failed' ||
            status == 'cancelled' ||
            status == 'expired') {
          throw Exception('Assistant run failed with status: $status');
        }
        // Continue polling for other statuses like 'in_progress', 'queued'
      } else {
        throw Exception(
          'Failed to check run status: ${response.statusCode} - ${response.body}',
        );
      }
    }
  }

  /// Get the latest message from the thread
  Future<void> _getLatestMessage() async {
    debugPrint('Getting latest message...');

    final response = await http.get(
      Uri.parse(
        'https://api.openai.com/v1/threads/$_threadId/messages?limit=1',
      ),
      headers: {
        'Authorization': 'Bearer $_apiKey',
        'OpenAI-Beta': 'assistants=v2',
      },
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      final messages = responseData['data'] as List;

      if (messages.isNotEmpty) {
        final latestMessage = messages.first;
        final content = latestMessage['content'] as List;

        if (content.isNotEmpty && content.first['type'] == 'text') {
          final assistantResponse = content.first['text']['value'];
          debugPrint('Assistant response: $assistantResponse');

          // Remove loading message
          _messages.removeWhere((msg) => msg.isLoading);

          // Add assistant response
          final assistantMessage = ChatMessage.assistant(
            content: assistantResponse,
          );
          _messages.add(assistantMessage);
        }
      }
    } else {
      throw Exception(
        'Failed to get messages: ${response.statusCode} - ${response.body}',
      );
    }
  }

  /// Clear conversation history
  void clearConversation() {
    _messages.clear();
    _threadId = null; // Reset thread ID to create a new conversation
    _clearError();
    _addWelcomeMessage();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Dispose resources
  @override
  void dispose() {
    super.dispose();
  }
}
