import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:befine/models/chat_message.dart';

/// Service for handling OpenAI Assistant API communication
class OpenAIService extends ChangeNotifier {
  static const String _apiKey =
      '********************************************************************************************************************************************************************';

  // Your specific patient assistant ID
  static const String _assistantId = 'asst_652dWmYXyXoLP87kf74OIgOy';

  List<ChatMessage> _messages = [];
  bool _isLoading = false;
  String? _error;
  String? _threadId; // Thread ID for the assistant conversation

  // Getters
  List<ChatMessage> get messages => List.unmodifiable(_messages);
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize the OpenAI service
  OpenAIService() {
    _initializeOpenAI();
    _addWelcomeMessage();
  }

  /// Initialize OpenAI service
  void _initializeOpenAI() {
    debugPrint(
      'Initializing OpenAI with API key: ${_apiKey.substring(0, 20)}...',
    );
    debugPrint('OpenAI initialized successfully');
  }

  /// Add welcome message from assistant
  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage.assistant(
      content:
          "Hello! I'm your specialized BeFine medical assistant. I'm here to help you with questions about your respiratory health, inhaler usage, medication management, and breathing techniques. How can I assist you today?",
    );
    _messages.add(welcomeMessage);
    notifyListeners();
  }

  /// Send a message and get response from OpenAI Assistant
  Future<void> sendMessage(String userMessage) async {
    if (userMessage.trim().isEmpty) return;

    // Clear any previous errors
    _clearError();

    // Add user message
    final userChatMessage = ChatMessage.user(content: userMessage.trim());
    _messages.add(userChatMessage);

    // Add loading message
    final loadingMessage = ChatMessage.loading();
    _messages.add(loadingMessage);

    _setLoading(true);
    notifyListeners();

    try {
      debugPrint('Sending message to OpenAI Assistant: $userMessage');

      // Step 1: Create or use existing thread
      if (_threadId == null) {
        await _createThread();
      }

      // Step 2: Add message to thread
      await _addMessageToThread(userMessage.trim());

      // Step 3: Run the assistant
      final runId = await _runAssistant();

      // Step 4: Wait for completion and get response
      await _waitForCompletion(runId);
    } catch (e) {
      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      // Add error message
      _setError('Failed to get response: ${e.toString()}');

      // Enhanced debugging
      debugPrint('=== DETAILED ERROR INFORMATION ===');
      debugPrint('Error: $e');
      debugPrint('Error type: ${e.runtimeType}');
      debugPrint('Error details: ${e.toString()}');
      debugPrint('=== END ERROR INFORMATION ===');

      // Provide more specific error messages based on the actual error
      String errorMessage;
      final errorString = e.toString().toLowerCase();

      if (errorString.contains('401') || errorString.contains('unauthorized')) {
        errorMessage =
            "I'm having trouble with my configuration. Please contact support.";
      } else if (errorString.contains('404') ||
          errorString.contains('not found')) {
        errorMessage =
            "I'm having trouble accessing my assistant configuration. Please contact support.";
      } else if (errorString.contains('429') ||
          errorString.contains('rate limit')) {
        errorMessage =
            "I'm currently experiencing high demand. Please try again in a few moments.";
      } else if (errorString.contains('500') ||
          errorString.contains('server')) {
        errorMessage =
            "I'm experiencing server issues. Please try again later.";
      } else if (errorString.contains('network') ||
          errorString.contains('connection') ||
          errorString.contains('timeout')) {
        errorMessage =
            "I'm having trouble connecting right now. Please check your internet connection and try again.";
      } else {
        errorMessage =
            "I'm experiencing a temporary issue. Please try asking your question again. (Debug: ${e.toString()})";
      }

      final assistantErrorMessage = ChatMessage.assistant(
        content: errorMessage,
      );
      _messages.add(assistantErrorMessage);
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  /// Create a new thread for the assistant conversation
  Future<void> _createThread() async {
    debugPrint('Creating new thread...');

    final response = await http.post(
      Uri.parse('https://api.openai.com/v1/threads'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'OpenAI-Beta': 'assistants=v2',
      },
      body: json.encode({}),
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      _threadId = responseData['id'];
      debugPrint('Thread created: $_threadId');
    } else {
      throw Exception(
        'Failed to create thread: ${response.statusCode} - ${response.body}',
      );
    }
  }

  /// Add a message to the thread
  Future<void> _addMessageToThread(String message) async {
    debugPrint('Adding message to thread: $message');

    final response = await http.post(
      Uri.parse('https://api.openai.com/v1/threads/$_threadId/messages'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'OpenAI-Beta': 'assistants=v2',
      },
      body: json.encode({'role': 'user', 'content': message}),
    );

    if (response.statusCode != 200) {
      throw Exception(
        'Failed to add message: ${response.statusCode} - ${response.body}',
      );
    }

    debugPrint('Message added to thread');
  }

  /// Run the assistant
  Future<String> _runAssistant() async {
    debugPrint('Running assistant...');

    final response = await http.post(
      Uri.parse('https://api.openai.com/v1/threads/$_threadId/runs'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'OpenAI-Beta': 'assistants=v2',
      },
      body: json.encode({
        'assistant_id': _assistantId,
        'tools': [], // Disable tools temporarily to avoid file_search issues
      }),
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      final runId = responseData['id'];
      debugPrint('Assistant run started: $runId');
      return runId;
    } else {
      throw Exception(
        'Failed to run assistant: ${response.statusCode} - ${response.body}',
      );
    }
  }

  /// Wait for the assistant run to complete and get the response
  Future<void> _waitForCompletion(String runId) async {
    debugPrint('Waiting for completion...');

    while (true) {
      await Future.delayed(const Duration(seconds: 1));

      final response = await http.get(
        Uri.parse('https://api.openai.com/v1/threads/$_threadId/runs/$runId'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'OpenAI-Beta': 'assistants=v2',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final status = responseData['status'];

        debugPrint('Run status: $status');

        if (status == 'completed') {
          await _getLatestMessage();
          break;
        } else if (status == 'failed' ||
            status == 'cancelled' ||
            status == 'expired') {
          // Get more details about the failure
          final lastError = responseData['last_error'];
          final failureReason =
              lastError != null ? lastError['message'] : 'Unknown error';
          final errorCode = lastError != null ? lastError['code'] : 'unknown';
          debugPrint(
            'Assistant run failed. Status: $status, Error: $failureReason, Code: $errorCode',
          );
          debugPrint('Full response: ${json.encode(responseData)}');

          // Remove loading message and show user-friendly error
          _messages.removeWhere((msg) => msg.isLoading);

          String userErrorMessage;
          if (errorCode == 'rate_limit_exceeded') {
            userErrorMessage =
                "I'm currently experiencing high demand. Please try again in a few moments.";
          } else if (errorCode == 'invalid_request') {
            userErrorMessage =
                "There's an issue with my configuration. Please contact support.";
          } else if (failureReason.toLowerCase().contains('tool')) {
            userErrorMessage =
                "I'm having trouble with my tools. Let me try a simpler response.";
          } else {
            userErrorMessage =
                "I'm experiencing a temporary issue. Please try asking your question again.";
          }

          final errorChatMessage = ChatMessage.assistant(
            content: userErrorMessage,
          );
          _messages.add(errorChatMessage);

          return; // Don't throw exception, just return
        }
        // Continue polling for other statuses like 'in_progress', 'queued'
      } else {
        throw Exception(
          'Failed to check run status: ${response.statusCode} - ${response.body}',
        );
      }
    }
  }

  /// Get the latest message from the thread
  Future<void> _getLatestMessage() async {
    debugPrint('Getting latest message...');

    final response = await http.get(
      Uri.parse(
        'https://api.openai.com/v1/threads/$_threadId/messages?limit=1',
      ),
      headers: {
        'Authorization': 'Bearer $_apiKey',
        'OpenAI-Beta': 'assistants=v2',
      },
    );

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      final messages = responseData['data'] as List;

      if (messages.isNotEmpty) {
        final latestMessage = messages.first;
        final content = latestMessage['content'] as List;

        if (content.isNotEmpty && content.first['type'] == 'text') {
          final assistantResponse = content.first['text']['value'];
          debugPrint('Assistant response: $assistantResponse');

          // Remove loading message
          _messages.removeWhere((msg) => msg.isLoading);

          // Add assistant response
          final assistantMessage = ChatMessage.assistant(
            content: assistantResponse,
          );
          _messages.add(assistantMessage);
        }
      }
    } else {
      throw Exception(
        'Failed to get messages: ${response.statusCode} - ${response.body}',
      );
    }
  }

  /// Clear conversation history
  void clearConversation() {
    _messages.clear();
    _threadId = null; // Reset thread ID to create a new conversation
    _clearError();
    _addWelcomeMessage();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Test API connection and assistant
  Future<void> testAPI() async {
    debugPrint('=== TESTING API CONNECTION ===');

    try {
      // Test 1: Check API key by listing assistants
      debugPrint('Test 1: Testing API key...');
      final assistantsResponse = await http.get(
        Uri.parse('https://api.openai.com/v1/assistants'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'OpenAI-Beta': 'assistants=v2',
        },
      );

      debugPrint('Assistants API Status: ${assistantsResponse.statusCode}');
      debugPrint('Assistants API Response: ${assistantsResponse.body}');

      if (assistantsResponse.statusCode == 200) {
        debugPrint('✅ API key is valid');

        // Test 2: Check if our specific assistant exists
        debugPrint('Test 2: Checking assistant $_assistantId...');
        final assistantResponse = await http.get(
          Uri.parse('https://api.openai.com/v1/assistants/$_assistantId'),
          headers: {
            'Authorization': 'Bearer $_apiKey',
            'OpenAI-Beta': 'assistants=v2',
          },
        );

        debugPrint('Assistant API Status: ${assistantResponse.statusCode}');
        debugPrint('Assistant API Response: ${assistantResponse.body}');

        if (assistantResponse.statusCode == 200) {
          debugPrint('✅ Assistant exists and is accessible');
        } else {
          debugPrint('❌ Assistant not found or not accessible');
        }
      } else {
        debugPrint('❌ API key is invalid or has issues');
      }
    } catch (e) {
      debugPrint('❌ Test failed with error: $e');
    }

    debugPrint('=== END API TEST ===');
  }

  /// Dispose resources
  @override
  void dispose() {
    super.dispose();
  }
}
