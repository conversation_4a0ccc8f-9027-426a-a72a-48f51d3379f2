import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:befine/models/chat_message.dart';

/// Service for handling OpenAI ChatGPT API communication
class OpenAIService extends ChangeNotifier {
  static const String _apiKey =
      '********************************************************************************************************************************************************************';

  List<ChatMessage> _messages = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<ChatMessage> get messages => List.unmodifiable(_messages);
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize the OpenAI service
  OpenAIService() {
    _initializeOpenAI();
    _addWelcomeMessage();
  }

  /// Initialize OpenAI service
  void _initializeOpenAI() {
    debugPrint(
      'Initializing OpenAI with API key: ${_apiKey.substring(0, 20)}...',
    );
    debugPrint('OpenAI initialized successfully');
  }

  /// Add welcome message from assistant
  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage.assistant(
      content:
          "Hello! I'm BeFine's Ai model your medical assistant. I'm here to help you with questions about your respiratory health, inhaler usage, and general medical concerns. How can I assist you today?",
    );
    _messages.add(welcomeMessage);
    notifyListeners();
  }

  /// Send a message and get response from ChatGPT
  Future<void> sendMessage(String userMessage) async {
    if (userMessage.trim().isEmpty) return;

    // Clear any previous errors
    _clearError();

    // Add user message
    final userChatMessage = ChatMessage.user(content: userMessage.trim());
    _messages.add(userChatMessage);

    // Add loading message
    final loadingMessage = ChatMessage.loading();
    _messages.add(loadingMessage);

    _setLoading(true);
    notifyListeners();

    try {
      debugPrint('Sending message to OpenAI: $userMessage');

      // Prepare the request body
      final requestBody = {
        'model': 'gpt-3.5-turbo',
        'messages': [
          {
            'role': 'system',
            'content':
                'You are a helpful medical assistant specializing in respiratory health and inhaler usage. '
                'Provide accurate, helpful information about respiratory conditions, inhaler techniques, '
                'medication adherence, and general health advice. Always remind users to consult with '
                'their healthcare provider for serious concerns. Keep responses concise and professional.',
          },
          {'role': 'user', 'content': userMessage.trim()},
        ],
        'max_tokens': 500,
        'temperature': 0.7,
      };

      debugPrint('Request body: ${json.encode(requestBody)}');

      // Make HTTP request to OpenAI API
      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode(requestBody),
      );

      debugPrint('Response status: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      if (response.statusCode == 200) {
        // Parse the response
        final responseData = json.decode(response.body);
        debugPrint('Parsed response: $responseData');

        if (responseData['choices'] != null &&
            responseData['choices'].isNotEmpty) {
          final choice = responseData['choices'][0];
          final messageContent = choice['message']['content'];

          debugPrint('Assistant response: $messageContent');

          final assistantMessage = ChatMessage.assistant(
            content:
                messageContent ??
                "I apologize, but I couldn't generate a response. Please try again.",
          );
          _messages.add(assistantMessage);
        } else {
          debugPrint('No choices in response');
          final errorMessage = ChatMessage.assistant(
            content:
                "I apologize, but I couldn't generate a response. Please try again.",
          );
          _messages.add(errorMessage);
        }
      } else {
        // Handle HTTP errors
        debugPrint('HTTP Error: ${response.statusCode}');
        debugPrint('Error body: ${response.body}');

        String errorMessage;
        if (response.statusCode == 401) {
          errorMessage =
              "I'm having trouble with my configuration. Please contact support.";
        } else if (response.statusCode == 429) {
          errorMessage =
              "I'm currently experiencing high demand. Please try again in a few moments.";
        } else if (response.statusCode >= 500) {
          errorMessage =
              "I'm experiencing server issues. Please try again later.";
        } else {
          errorMessage =
              "I'm experiencing a temporary issue. Please try asking your question again.";
        }

        final assistantErrorMessage = ChatMessage.assistant(
          content: errorMessage,
        );
        _messages.add(assistantErrorMessage);
      }
    } catch (e) {
      // Remove loading message
      _messages.removeWhere((msg) => msg.isLoading);

      // Add error message
      _setError('Failed to get response: ${e.toString()}');

      // Provide more specific error messages
      String errorMessage;
      if (e.toString().contains('API key')) {
        errorMessage =
            "I'm having trouble with my configuration. Please contact support.";
      } else if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        errorMessage =
            "I'm having trouble connecting right now. Please check your internet connection and try again.";
      } else if (e.toString().contains('quota') ||
          e.toString().contains('limit')) {
        errorMessage =
            "I'm currently experiencing high demand. Please try again in a few moments.";
      } else {
        errorMessage =
            "I'm experiencing a temporary issue. Please try asking your question again.";
      }

      final assistantErrorMessage = ChatMessage.assistant(
        content: errorMessage,
      );
      _messages.add(assistantErrorMessage);

      debugPrint('OpenAI API Error: $e');
      debugPrint('Error type: ${e.runtimeType}');
      debugPrint('Error details: ${e.toString()}');
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  /// Clear conversation history
  void clearConversation() {
    _messages.clear();
    _clearError();
    _addWelcomeMessage();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Dispose resources
  @override
  void dispose() {
    super.dispose();
  }
}
