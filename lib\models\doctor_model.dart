/// Doctor model class for storing and managing doctor information
/// Designed for future Supabase integration
class Doctor<PERSON><PERSON><PERSON> {
  /// Required fields
  final String firstName;
  final String lastName;
  final String gender;
  final String phoneNumber;
  final String email;
  final String password;

  /// Optional fields with default values
  final String specialty;
  final String address;

  DoctorModel({
    // Required fields
    required this.firstName,
    required this.lastName,
    required this.gender,
    required this.phoneNumber,
    required this.email,
    required this.password,

    // Optional fields with default values
    this.specialty = 'Unknown',
    this.address = 'Unknown',
  });

  /// Demo doctor data for testing and development
  static List<DoctorModel> demoDoctors = [
    DoctorModel(
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      specialty: 'Pulmonologist',
      address: '123 Medical Center, New York',
      email: '<EMAIL>',
      password: 'Password123!',
      gender: 'male',
      phoneNumber: '+**********',
    ),
    Doctor<PERSON><PERSON><PERSON>(
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      specialty: 'Allergist',
      address: '456 Health Clinic, Boston',
      email: '<EMAIL>',
      password: 'SecurePass456!',
      gender: 'female',
      phoneNumber: '+**********',
    ),
  ];
}
